<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Stripe Payment Test</title>
  <script src="https://js.stripe.com/v3/"></script>
  <style>
    body { font-family: sans-serif; padding: 30px; }
    #card-element { padding: 10px; border: 1px solid #ccc; border-radius: 6px; margin-bottom: 15px; }
    #pay-button { padding: 10px 20px; background: #6772e5; color: white; border: none; border-radius: 6px; cursor: pointer; }
  </style>
</head>
<body>
  <h2>Stripe Payment Test</h2>
  <div id="card-element"></div>
  <button id="pay-button">Pay</button>
  <div id="result"></div>

  <script>
    const stripe = Stripe("");

    const clientSecret = "pi_3SAfzpFocBKD2A5Z03iau3Qw_secret_a4szTYaAgoh7Fvp0phjypTz94";

    const elements = stripe.elements();
    const cardElement = elements.create("card");
    cardElement.mount("#card-element");

    document.getElementById("pay-button").addEventListener("click", async () => {
      const { paymentIntent, error } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
        }
      });

      if (error) {
        document.getElementById("result").innerText = "❌ Payment failed: " + error.message;
      } else {
        document.getElementById("result").innerText = "✅ Payment succeeded! ID: " + paymentIntent.id;
      }
    });
  </script>
</body>
</html>