# Stripe Integration - Ava Mobile App Backend

This document provides a comprehensive guide to the Stripe integration for subscription and credits management in the Ava Mobile App backend.

## Overview

The Stripe integration provides:
- **3 Subscription Plans**: Starting ($9.99), Scaling ($19.99), Summit ($39.99)
- **Credits System**: Monthly credit allocation based on subscription plan
- **Webhook Processing**: Automatic credit allocation on successful payments
- **Secure Payment Processing**: Production-ready Stripe integration

## Architecture

### Core Components

1. **StripeService** (`src/services/stripe/index.ts`)
   - Customer management
   - Subscription creation and management
   - Webhook event processing
   - Credits allocation

2. **CreditsManager** (`src/services/credits/index.ts`)
   - Credits consumption and tracking
   - Usage history
   - Automatic credits reset (cron job)

3. **Routes**
   - `/api/subscriptions/*` - Subscription management
   - `/api/webhook/stripe` - Stripe webhook endpoint

4. **Database Schema**
   - `subscription_plans` - Plan definitions
   - `user_subscriptions` - User subscription records
   - `user_credits` - Credits tracking
   - `credits_usage_history` - Usage audit trail
   - `stripe_webhook_events` - Webhook idempotency

## Subscription Plans

### Starting Plan - $9.99/month
- 80 monthly credits
- Basic AI chat support
- Email support
- Standard response time

### Scaling Plan - $19.99/month
- 160 monthly credits
- Advanced AI chat support
- Priority email support
- Voice messages
- Faster response time

### Summit Plan - $39.99/month
- 400 monthly credits
- Premium AI chat support
- 24/7 priority support
- Voice messages
- Instant response time
- Custom integrations

## Setup Instructions

### 1. Environment Variables

Add the following to your `.env` file:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
```

### 2. Database Migration

Run the database migrations:

```bash
# Apply the subscription schema
psql -d your_database -f migrations/stripe_subscriptions_schema.sql

# Add Stripe customer ID to profiles
psql -d your_database -f migrations/add_stripe_customer_to_profiles.sql
```

### 3. Create Stripe Products

Run the setup script to create products and prices in Stripe:

```bash
npm run setup-stripe
```

This will:
- Create 3 products in Stripe
- Create monthly recurring prices
- Update the database with Stripe IDs

### 4. Configure Webhook

1. Go to your Stripe Dashboard
2. Navigate to Webhooks
3. Add endpoint: `https://your-domain.com/api/webhook/stripe`
4. Select events:
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
5. Copy the webhook secret to `STRIPE_WEBHOOK_SECRET`

## API Endpoints

### Subscription Management

#### Get Subscription Plans
```http
GET /api/subscriptions/plans
```

Response:
```json
{
  "success": true,
  "data": [
    {
      "id": "plan-id",
      "plan_name": "starting",
      "display_name": "Starting Plan",
      "price_amount": 999,
      "credits_included": 80,
      "features": {...}
    }
  ]
}
```

#### Create Subscription
```http
POST /api/subscriptions/create-subscription
Authorization: Bearer <token>
Content-Type: application/json

{
  "priceId": "price_1234567890"
}
```

Response:
```json
{
  "success": true,
  "data": {
    "subscription": {...},
    "clientSecret": "pi_1234567890_secret_xyz",
    "plan": {
      "name": "Starting Plan",
      "credits": 80,
      "price": 9.99
    }
  }
}
```

#### Get Current Subscription
```http
GET /api/subscriptions/current
Authorization: Bearer <token>
```

#### Cancel Subscription
```http
POST /api/subscriptions/cancel
Authorization: Bearer <token>
Content-Type: application/json

{
  "cancelAtPeriodEnd": true
}
```

#### Use Credits
```http
POST /api/subscriptions/use-credits
Authorization: Bearer <token>
Content-Type: application/json

{
  "credits": 1,
  "actionType": "chat_message",
  "description": "AI chat response"
}
```

### Webhook Endpoint

```http
POST /api/webhook/stripe
Content-Type: application/json
Stripe-Signature: <signature>

{
  "id": "evt_1234567890",
  "type": "invoice.payment_succeeded",
  "data": {...}
}
```

## Credits System

### Credit Costs

Default credit costs per action:
- Chat message: 1 credit
- Voice transcription: 2 credits
- AI response: 1 credit
- File upload: 1 credit
- Support ticket: 0 credits (free)

### Usage Tracking

All credit usage is tracked in the `credits_usage_history` table with:
- User ID
- Credits used
- Action type
- Description
- Timestamp

### Automatic Reset

Credits are automatically reset monthly based on the user's subscription:
- Runs daily at 2 AM UTC
- Resets credits for users whose billing cycle has ended
- Allocates new credits based on current plan

## Middleware

### Credits Middleware

```typescript
import { requireCredits, consumeCredits } from '@/middleware/credits'

// Check if user has enough credits
router.post('/action', requireCredits('chat_message'), (req, res) => {
  // Action logic here
})

// Consume credits after successful action
router.post('/action', 
  requireCredits('chat_message'),
  // ... action logic ...
  consumeCredits('chat_message', 'User chat message'),
  (req, res) => {
    res.json({ success: true })
  }
)
```

### Subscription Middleware

```typescript
import { requireActiveSubscription, requirePlan } from '@/middleware/subscription'

// Require any active subscription
router.get('/premium-feature', requireActiveSubscription, handler)

// Require specific plan or higher
router.get('/advanced-feature', requirePlan('scaling'), handler)
```

## Testing

### Unit Tests
```bash
npm run test:stripe
```

### Integration Tests
```bash
npm run test:integration
```

### Manual Testing

1. Start the server: `npm run dev`
2. Run integration tests: `npm run test:integration`
3. Test subscription flow in frontend
4. Test webhook events using Stripe CLI:
   ```bash
   stripe listen --forward-to localhost:3000/api/webhook/stripe
   stripe trigger invoice.payment_succeeded
   ```

## Security Features

### Webhook Security
- Signature verification using Stripe webhook secret
- Rate limiting (100 requests per minute per IP)
- Payload size validation (max 1MB)
- Request logging and monitoring
- Idempotency protection

### API Security
- JWT authentication required for all subscription endpoints
- Input validation using express-validator
- SQL injection protection
- XSS protection
- CORS configuration

## Monitoring and Logging

### Key Metrics to Monitor
- Subscription creation success rate
- Webhook processing success rate
- Credits usage patterns
- Payment failure rates
- API response times

### Logs to Watch
- Webhook event processing
- Subscription lifecycle events
- Credits allocation and usage
- Payment failures
- API errors

## Troubleshooting

### Common Issues

1. **Webhook signature verification fails**
   - Check `STRIPE_WEBHOOK_SECRET` is correct
   - Ensure raw body is passed to verification

2. **Credits not allocated after payment**
   - Check webhook events are being processed
   - Verify subscription metadata contains user_id
   - Check database for webhook event records

3. **Subscription creation fails**
   - Verify Stripe keys are correct
   - Check customer creation in Stripe
   - Ensure plan exists in database

### Debug Commands

```bash
# Check webhook events
SELECT * FROM stripe_webhook_events ORDER BY created_at DESC LIMIT 10;

# Check user credits
SELECT * FROM user_credits WHERE user_id = 'user-id';

# Check subscription status
SELECT * FROM user_subscriptions WHERE user_id = 'user-id';
```

## Production Deployment

### Environment Setup
1. Use live Stripe keys (`sk_live_*`, `pk_live_*`)
2. Configure production webhook endpoint
3. Set up monitoring and alerting
4. Enable database backups
5. Configure SSL/TLS

### Performance Considerations
- Database connection pooling
- Redis for rate limiting (production)
- CDN for static assets
- Load balancing for high availability

## Support

For issues related to the Stripe integration:
1. Check the logs for error messages
2. Verify environment configuration
3. Test webhook endpoints manually
4. Contact development team with specific error details

---

**Last Updated**: January 2025
**Version**: 1.0.0
