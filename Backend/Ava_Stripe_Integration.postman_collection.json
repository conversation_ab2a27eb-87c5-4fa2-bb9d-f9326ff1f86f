{"info": {"name": "Ava Mobile App - Stripe Integration", "description": "Complete API collection for testing Stripe subscription and credits management", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "authToken", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"TestPassword123!\",\n  \"name\": \"Test User\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.tokens) {", "        pm.collectionVariables.set('authToken', response.tokens.accessToken);", "    }", "}"]}}]}, {"name": "Login User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"TestPassword123!\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.tokens) {", "        pm.collectionVariables.set('authToken', response.tokens.accessToken);", "    }", "}"]}}]}]}, {"name": "Subscription Management", "item": [{"name": "Get Subscription Plans", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/subscriptions/plans", "host": ["{{baseUrl}}"], "path": ["api", "subscriptions", "plans"]}}}, {"name": "Get Current Subscription", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/api/subscriptions/current", "host": ["{{baseUrl}}"], "path": ["api", "subscriptions", "current"]}}}, {"name": "Create Subscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"priceId\": \"price_starting_placeholder\"\n}"}, "url": {"raw": "{{baseUrl}}/api/subscriptions/create-subscription", "host": ["{{baseUrl}}"], "path": ["api", "subscriptions", "create-subscription"]}}}, {"name": "Cancel Subscription", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"cancelAtPeriodEnd\": true\n}"}, "url": {"raw": "{{baseUrl}}/api/subscriptions/cancel", "host": ["{{baseUrl}}"], "path": ["api", "subscriptions", "cancel"]}}}]}, {"name": "Credits Management", "item": [{"name": "Use Credits", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"credits\": 1,\n  \"actionType\": \"chat_message\",\n  \"description\": \"Test credit usage\"\n}"}, "url": {"raw": "{{baseUrl}}/api/subscriptions/use-credits", "host": ["{{baseUrl}}"], "path": ["api", "subscriptions", "use-credits"]}}}]}, {"name": "Webhook Testing", "item": [{"name": "Test Webhook Endpoint (<PERSON> - No Signature)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"evt_test123\",\n  \"type\": \"invoice.payment_succeeded\",\n  \"data\": {\n    \"object\": {\n      \"subscription\": \"sub_test123\"\n    }\n  }\n}"}, "url": {"raw": "{{baseUrl}}/api/webhook/stripe", "host": ["{{baseUrl}}"], "path": ["api", "webhook", "stripe"]}}}]}, {"name": "Health Check", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}]}]}