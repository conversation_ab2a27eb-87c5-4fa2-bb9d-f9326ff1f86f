# 🧪 Test Mode Setup with ngrok

## Step-by-Step Setup Guide

### 1. Get Stripe Test Keys

1. **Go to Stripe Dashboard**: https://dashboard.stripe.com
2. **Switch to Test Mode** (toggle in top-left corner)
3. **Navigate to**: Developers → API keys
4. **Copy these keys**:
   ```
   Secret key: sk_test_... (starts with sk_test_)
   Publishable key: pk_test_... (starts with pk_test_)
   ```

### 2. Update .env File

Replace the placeholder values in your `.env` file:

```env
# Stripe Configuration (TEST MODE)
STRIPE_SECRET_KEY=sk_test_your_actual_test_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_actual_webhook_secret_here  
STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_publishable_key_here
```

### 3. Create Webhook in Stripe Dashboard

1. **In Stripe Dashboard** (Test Mode):
   - Go to **Developers** → **Webhooks**
   - Click **"+ Add endpoint"**

2. **Webhook Settings**:
   ```
   Endpoint URL: https://undenied-chronic-jayce.ngrok-free.dev/api/webhook/stripe
   Description: Ava Mobile App Webhook (Test)
   ```

3. **Select Events** (click "Select events"):
   - Search and select these 4 events:
     - ✅ `invoice.payment_succeeded`
     - ✅ `invoice.payment_failed`
     - ✅ `customer.subscription.updated`
     - ✅ `customer.subscription.deleted`

4. **Create Endpoint**

5. **Get Webhook Secret**:
   - Click on your newly created webhook
   - In the "Signing secret" section, click **"Reveal"**
   - Copy the secret (starts with `whsec_`)
   - Update your `.env` file with this secret

### 4. Create Database Tables

If you haven't already, create the database tables in Supabase:

1. **Go to Supabase Dashboard** → SQL Editor
2. **Run this SQL**:

```sql
-- Create subscription_plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  stripe_product_id TEXT UNIQUE NOT NULL,
  stripe_price_id TEXT UNIQUE NOT NULL,
  plan_name TEXT UNIQUE NOT NULL,
  display_name TEXT NOT NULL,
  description TEXT,
  price_amount INTEGER NOT NULL,
  currency TEXT NOT NULL DEFAULT 'usd',
  billing_interval TEXT NOT NULL DEFAULT 'month',
  credits_included INTEGER NOT NULL DEFAULT 0,
  features JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Insert test subscription plans
INSERT INTO subscription_plans (
  stripe_product_id, 
  stripe_price_id, 
  plan_name, 
  display_name, 
  description, 
  price_amount, 
  currency,
  billing_interval,
  credits_included,
  features
) VALUES 
(
  'prod_starting_test', 
  'price_starting_test',
  'starting',
  'Starting Plan',
  'Perfect for getting started with AI assistance',
  999,
  'usd',
  'month',
  80,
  '{"ai_chat_support": "basic", "email_support": true, "response_time": "standard"}'::jsonb
),
(
  'prod_scaling_test',
  'price_scaling_test', 
  'scaling',
  'Scaling Plan',
  'Advanced features for growing businesses',
  1999,
  'usd',
  'month',
  160,
  '{"ai_chat_support": "advanced", "email_support": "priority", "response_time": "faster"}'::jsonb
),
(
  'prod_summit_test',
  'price_summit_test',
  'summit', 
  'Summit Plan',
  'Premium experience with all features included',
  3999,
  'usd',
  'month',
  400,
  '{"ai_chat_support": "premium", "email_support": "24/7_priority", "response_time": "instant"}'::jsonb
)
ON CONFLICT (stripe_product_id) DO NOTHING;
```

### 5. Create Stripe Products

After updating your `.env` with real test keys:

```bash
npm run setup-stripe
```

This will create the actual products and prices in Stripe and update your database.

### 6. Test Your Setup

1. **Restart your server**:
   ```bash
   npm run dev
   ```

2. **Test subscription plans endpoint**:
   ```bash
   curl https://undenied-chronic-jayce.ngrok-free.dev/api/subscriptions/plans
   ```

3. **Test webhook endpoint**:
   ```bash
   curl -X POST https://undenied-chronic-jayce.ngrok-free.dev/api/webhook/stripe \
     -H "Content-Type: application/json" \
     -d '{"test": "data"}'
   ```
   (This should fail with "Missing Stripe signature" - which is correct!)

### 7. Test with Stripe CLI (Optional)

For advanced testing, use Stripe CLI:

```bash
# Install Stripe CLI
# Then login
stripe login

# Test webhook events
stripe trigger invoice.payment_succeeded

# Listen to events (alternative to dashboard webhook)
stripe listen --forward-to localhost:3000/api/webhook/stripe
```

## 🧪 Testing Payment Flow

### Test Credit Cards (Stripe Test Mode)

Use these test card numbers:

- **Success**: `****************`
- **Decline**: `****************`
- **Insufficient Funds**: `****************`

**Test Details**:
- Any future expiry date (e.g., 12/25)
- Any 3-digit CVC
- Any ZIP code

### Test Subscription Flow

1. **Create a test user** in your app
2. **Get auth token** from login
3. **Create subscription** using Postman:
   ```json
   POST https://undenied-chronic-jayce.ngrok-free.dev/api/subscriptions/create-subscription
   Authorization: Bearer YOUR_TOKEN
   {
     "priceId": "price_starting_test"
   }
   ```

## ✅ Verification Checklist

- [ ] Stripe dashboard shows "Test Mode" 
- [ ] Test keys in `.env` file
- [ ] Webhook created with ngrok URL
- [ ] Webhook secret updated in `.env`
- [ ] Database tables created
- [ ] Server starts without errors
- [ ] `/api/subscriptions/plans` returns data
- [ ] Webhook endpoint responds (even with error is OK)

## 🚨 Common Issues

**"Could not find table"**: Create database tables in Supabase
**"Invalid API key"**: Check you're using test keys in test mode
**"Webhook signature failed"**: Verify webhook secret is correct
**"ngrok not found"**: Make sure ngrok is running on port 3000

---

Once you complete these steps, your test environment will be fully functional! 🎉
