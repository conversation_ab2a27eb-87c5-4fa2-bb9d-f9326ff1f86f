# 🚀 Stripe Integration Setup Instructions

## ⚠️ IMPORTANT: Database Setup Required

The Stripe integration requires database tables that need to be created manually in your Supabase dashboard.

### Step 1: Create Database Tables

1. **Go to your Supabase Dashboard**: https://supabase.com/dashboard
2. **Navigate to**: Your Project → SQL Editor
3. **Run the following SQL commands** one by one:

#### Create subscription_plans table:
```sql
CREATE TABLE subscription_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  stripe_product_id TEXT UNIQUE NOT NULL,
  stripe_price_id TEXT UNIQUE NOT NULL,
  plan_name TEXT UNIQUE NOT NULL,
  display_name TEXT NOT NULL,
  description TEXT,
  price_amount INTEGER NOT NULL,
  currency TEXT NOT NULL DEFAULT 'usd',
  billing_interval TEXT NOT NULL DEFAULT 'month',
  credits_included INTEGER NOT NULL DEFAULT 0,
  features J<PERSON>N<PERSON> DEFAULT '{}',
  is_active BO<PERSON>EAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Create user_subscriptions table:
```sql
CREATE TABLE user_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  subscription_plan_id UUID NOT NULL REFERENCES subscription_plans(id),
  stripe_subscription_id TEXT UNIQUE NOT NULL,
  stripe_customer_id TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'active',
  current_period_start TIMESTAMPTZ,
  current_period_end TIMESTAMPTZ,
  cancel_at_period_end BOOLEAN DEFAULT false,
  canceled_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Create user_credits table:
```sql
CREATE TABLE user_credits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  current_credits INTEGER NOT NULL DEFAULT 0,
  total_credits_allocated INTEGER NOT NULL DEFAULT 0,
  credits_used_this_period INTEGER NOT NULL DEFAULT 0,
  last_reset_date TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id)
);
```

#### Create credits_usage_history table:
```sql
CREATE TABLE credits_usage_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  credits_used INTEGER NOT NULL,
  action_type TEXT NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Create stripe_webhook_events table:
```sql
CREATE TABLE stripe_webhook_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  stripe_event_id TEXT UNIQUE NOT NULL,
  event_type TEXT NOT NULL,
  processed BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Insert subscription plans:
```sql
INSERT INTO subscription_plans (
  stripe_product_id, 
  stripe_price_id, 
  plan_name, 
  display_name, 
  description, 
  price_amount, 
  currency,
  billing_interval,
  credits_included,
  features
) VALUES 
(
  'prod_starting_placeholder', 
  'price_starting_placeholder',
  'starting',
  'Starting Plan',
  'Perfect for getting started with AI assistance',
  999,
  'usd',
  'month',
  80,
  '{"ai_chat_support": "basic", "email_support": true, "response_time": "standard", "voice_messages": false, "priority_support": false, "custom_integrations": false}'::jsonb
),
(
  'prod_scaling_placeholder',
  'price_scaling_placeholder', 
  'scaling',
  'Scaling Plan',
  'Advanced features for growing businesses',
  1999,
  'usd',
  'month',
  160,
  '{"ai_chat_support": "advanced", "email_support": "priority", "response_time": "faster", "voice_messages": true, "priority_support": false, "custom_integrations": false}'::jsonb
),
(
  'prod_summit_placeholder',
  'price_summit_placeholder',
  'summit', 
  'Summit Plan',
  'Premium experience with all features included',
  3999,
  'usd',
  'month',
  400,
  '{"ai_chat_support": "premium", "email_support": "24/7_priority", "response_time": "instant", "voice_messages": true, "priority_support": true, "custom_integrations": true}'::jsonb
)
ON CONFLICT (stripe_product_id) DO NOTHING;
```

#### Add stripe_customer_id to profiles table:
```sql
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;
```

### Step 2: Configure Stripe

1. **Get your Stripe keys** from https://dashboard.stripe.com/apikeys
2. **Update your `.env` file**:
   ```env
   STRIPE_SECRET_KEY=sk_test_your_actual_stripe_secret_key
   STRIPE_WEBHOOK_SECRET=whsec_your_actual_webhook_secret
   STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_publishable_key
   ```

3. **Create Stripe products** (run this after setting up real Stripe keys):
   ```bash
   npm run setup-stripe
   ```

### Step 3: Set up Webhook Endpoint

1. **Go to Stripe Dashboard** → Webhooks
2. **Add endpoint**: `https://your-domain.com/api/webhook/stripe`
3. **Select events**:
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
4. **Copy webhook secret** to your `.env` file

### Step 4: Test the Integration

1. **Restart your server**:
   ```bash
   npm run dev
   ```

2. **Test the endpoints**:
   ```bash
   # Test subscription plans
   curl http://localhost:3000/api/subscriptions/plans
   
   # Run integration tests
   npm run test:integration
   ```

3. **Import Postman collection**: `Ava_Stripe_Integration.postman_collection.json`

## ✅ Verification

After completing the setup, you should see:

1. **Server starts without errors**
2. **Subscription plans endpoint returns data**:
   ```json
   {
     "success": true,
     "data": [
       {
         "plan_name": "starting",
         "display_name": "Starting Plan",
         "price_amount": 999,
         "credits_included": 80
       }
     ]
   }
   ```

3. **Stripe service shows as enabled** in server logs

## 🔧 Troubleshooting

### "Could not find the table 'public.subscription_plans'"
- Make sure you've run all the SQL commands in Supabase dashboard
- Check that the tables exist in Database → Tables

### "Invalid Stripe keys"
- Verify your Stripe keys are correct and not placeholders
- Make sure you're using test keys for development

### "Webhook signature verification failed"
- Ensure webhook secret matches the one from Stripe dashboard
- Check that webhook endpoint is configured correctly

## 📞 Support

If you encounter issues:
1. Check the server logs for specific error messages
2. Verify all environment variables are set correctly
3. Ensure database tables are created properly
4. Test with the provided Postman collection

---

**Next Steps**: Once setup is complete, you can start creating subscriptions and testing the full payment flow!
