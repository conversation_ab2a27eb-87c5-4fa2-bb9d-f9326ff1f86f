# 🧪 Complete Stripe Testing Guide

## 🎯 Your Current Setup

✅ **Server**: Running on port 3001  
✅ **Stripe**: Test mode with real products created  
✅ **Plans**: 3 subscription plans ready  
✅ **ngrok**: https://undenied-chronic-jayce.ngrok-free.dev  

## 📋 **Step 1: Update ngrok (IMPORTANT)**

Your server is now on port 3001, so update ngrok:

```bash
# Stop current ngrok
# Start new ngrok pointing to port 3001
ngrok http 3001
```

**Update your Stripe webhook URL to:**
```
https://undenied-chronic-jayce.ngrok-free.dev/api/webhook/stripe
```

## 🧪 **Step 2: Test Subscription Plans**

```bash
# Test 1: Get subscription plans
curl -X GET https://undenied-chronic-jayce.ngrok-free.dev/api/subscriptions/plans

# Expected: Returns 3 plans with real Stripe IDs
```


## 👤 **Step 3: Create Test User & Get Auth Token**

```bash
# Register a test user
curl -X POST https://undenied-chronic-jayce.ngrok-free.dev/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPassword123!",
    "name": "Test User"
  }'

# Save the accessToken from response
```

## 💳 **Step 4: Create Subscription (This is where payment happens)**

```bash
# Create subscription for Starting Plan
curl -X POST https://undenied-chronic-jayce.ngrok-free.dev/api/subscriptions/create-subscription \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "priceId": "price_1SAKm4FocBKD2A5ZPY3bXJvX"
  }'

# This returns a clientSecret for payment confirmation
```

## 🎯 **How Credits Work - Complete Flow**

### **Credit Allocation Process:**

1. **User subscribes** → Stripe creates subscription
2. **Payment succeeds** → Stripe sends webhook `invoice.payment_succeeded`
3. **Your webhook processes** → Credits allocated automatically
4. **User gets credits** → 80 credits for Starting Plan

### **Credit Usage Process:**

```bash
# Check current credits
curl -X GET https://undenied-chronic-jayce.ngrok-free.dev/api/subscriptions/current \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN_HERE"

# Use credits (deduct from account)
curl -X POST https://undenied-chronic-jayce.ngrok-free.dev/api/subscriptions/use-credits \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "credits": 5,
    "actionType": "chat_message",
    "description": "AI chat response"
  }'
```

## 🔄 **Credit Costs in Your System:**

```javascript
// Default credit costs (configurable)
const CREDIT_COSTS = {
  'chat_message': 1,        // Each AI chat costs 1 credit
  'voice_transcription': 2, // Voice to text costs 2 credits
  'ai_response': 1,         // AI response costs 1 credit
  'file_upload': 1,         // File upload costs 1 credit
  'support_ticket': 0       // Support tickets are free
}
```

## 🎮 **Testing Credit Deduction & Restoration**

### **Scenario 1: User Uses Credits**
```bash
# 1. Check initial credits (should be 80 for Starting Plan)
curl -X GET https://undenied-chronic-jayce.ngrok-free.dev/api/subscriptions/current \
  -H "Authorization: Bearer YOUR_TOKEN"

# 2. Use 10 credits
curl -X POST https://undenied-chronic-jayce.ngrok-free.dev/api/subscriptions/use-credits \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"credits": 10, "actionType": "chat_message", "description": "Test usage"}'

# 3. Check credits again (should be 70 now)
curl -X GET https://undenied-chronic-jayce.ngrok-free.dev/api/subscriptions/current \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### **Scenario 2: Monthly Credit Reset**
Credits are automatically reset monthly when:
- User's billing cycle renews
- Payment succeeds
- Webhook `invoice.payment_succeeded` is received

### **Scenario 3: Subscription Cancellation**
```bash
# Cancel subscription (credits remain until period ends)
curl -X POST https://undenied-chronic-jayce.ngrok-free.dev/api/subscriptions/cancel \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"cancelAtPeriodEnd": true}'
```

## 🔗 **Webhook Testing**

### **Test Webhook Security:**
```bash
# This should fail (no signature)
curl -X POST https://undenied-chronic-jayce.ngrok-free.dev/api/webhook/stripe \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# Expected: "Missing Stripe signature" error
```

### **Test Real Webhook Events:**
Use Stripe CLI to trigger events:

```bash
# Install Stripe CLI first
stripe login

# Trigger payment success (this allocates credits)
stripe trigger invoice.payment_succeeded

# Trigger payment failure
stripe trigger invoice.payment_failed

# Trigger subscription update
stripe trigger customer.subscription.updated
```

## 💰 **Test Payment Flow with Test Cards**

### **Stripe Test Cards:**
- **Success**: `****************`
- **Decline**: `****************`
- **Insufficient Funds**: `****************`
- **Requires Authentication**: `****************`

**Test Details:**
- Expiry: Any future date (12/25)
- CVC: Any 3 digits (123)
- ZIP: Any ZIP code (12345)

## 🔄 **Integration with Your Mobile App**

### **Frontend Integration Steps:**

1. **Get subscription plans:**
   ```javascript
   fetch('https://undenied-chronic-jayce.ngrok-free.dev/api/subscriptions/plans')
   ```

2. **Create subscription:**
   ```javascript
   fetch('https://undenied-chronic-jayce.ngrok-free.dev/api/subscriptions/create-subscription', {
     method: 'POST',
     headers: {
       'Authorization': `Bearer ${userToken}`,
       'Content-Type': 'application/json'
     },
     body: JSON.stringify({ priceId: 'price_1SAKm4FocBKD2A5ZPY3bXJvX' })
   })
   ```

3. **Use Stripe Elements for payment:**
   ```javascript
   // Use the clientSecret returned from step 2
   stripe.confirmCardPayment(clientSecret, {
     payment_method: {
       card: cardElement,
       billing_details: { name: 'Customer Name' }
     }
   })
   ```

4. **Check credits before actions:**
   ```javascript
   // Before AI chat, voice transcription, etc.
   const credits = await fetch('/api/subscriptions/current', {
     headers: { 'Authorization': `Bearer ${token}` }
   })
   ```

5. **Deduct credits after actions:**
   ```javascript
   // After successful AI response
   await fetch('/api/subscriptions/use-credits', {
     method: 'POST',
     headers: {
       'Authorization': `Bearer ${token}`,
       'Content-Type': 'application/json'
     },
     body: JSON.stringify({
       credits: 1,
       actionType: 'chat_message',
       description: 'AI chat response'
     })
   })
   ```

## 📊 **Monitoring & Analytics**

### **Track Credit Usage:**
```bash
# Get usage history
curl -X GET https://undenied-chronic-jayce.ngrok-free.dev/api/subscriptions/current \
  -H "Authorization: Bearer YOUR_TOKEN"

# Response includes:
# - current_credits: remaining credits
# - total_credits_allocated: monthly allocation
# - credits_used_this_period: used this month
```

## 🚨 **Common Issues & Solutions**

### **"Insufficient credits" Error:**
- User has used all monthly credits
- Show upgrade prompt or wait for next billing cycle

### **"Invalid subscription" Error:**
- User subscription expired or cancelled
- Prompt user to resubscribe

### **Webhook not processing:**
- Check webhook secret is correct
- Verify ngrok URL is updated
- Check server logs for errors

## 🎯 **Production Checklist**

- [ ] Switch to live Stripe keys
- [ ] Update webhook URL to production domain
- [ ] Set up monitoring for webhook failures
- [ ] Configure proper error handling
- [ ] Set up credit usage alerts
- [ ] Test subscription lifecycle completely

---

**Your system is now fully functional!** 🎉

The credit system automatically:
- ✅ Allocates credits when payment succeeds
- ✅ Deducts credits when users take actions
- ✅ Resets credits monthly on billing cycle
- ✅ Handles subscription cancellations
- ✅ Tracks usage history for analytics
